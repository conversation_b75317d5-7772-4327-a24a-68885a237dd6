import { useEffect, useRef } from "react";
import { useGSAP } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import {
  PROJECTS,
  ANIMATION_DURATIONS,
  ANIMATION_EASINGS,
  COLOR_VARIANTS
} from '@/constants';

export default function PortfolioSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const cardsRef = useRef<HTMLDivElement[]>([]);
  const { gsap, animate, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  useEffect(() => {
    if (isAvailable && sectionRef.current) {
      initializeAnimations();
    }
  }, [isAvailable]);

  const initializeAnimations = () => {
    try {
      if (!gsap || !titleRef.current) return;

      const accessibility = getAccessibilityConfig();

      if (accessibility.reduceMotion) {
        // Set final states immediately for reduced motion
        if (titleRef.current) {
          gsap.set(titleRef.current, { opacity: 1, y: 0 });
        }
        cardsRef.current.forEach((card) => {
          if (card) {
            gsap.set(card, { opacity: 1, y: 0, scale: 1 });
          }
        });
        return;
      }

      // Animate section title with scroll trigger
      gsap.fromTo(titleRef.current,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: ANIMATION_DURATIONS.SLOW,
          ease: ANIMATION_EASINGS.POWER2_OUT,
          scrollTrigger: {
            trigger: titleRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Animate project cards with scroll triggers
      cardsRef.current.forEach((card, index) => {
        if (card) {
          gsap.fromTo(card,
            { 
              opacity: 0, 
              y: 80, 
              scale: 0.9 
            },
            {
              opacity: 1,
              y: 0,
              scale: 1,
              duration: ANIMATION_DURATIONS.SLOW,
              delay: index * 0.2,
              ease: ANIMATION_EASINGS.POWER2_OUT,
              scrollTrigger: {
                trigger: card,
                start: "top 85%",
                end: "bottom 20%",
                toggleActions: "play none none reverse"
              }
            }
          );
        }
      });
    } catch (error) {
      handleError(error as Error);
    }
  };

  const handleCardHover = (index: number, isEntering: boolean) => {
    const card = cardsRef.current[index];
    if (!card || !gsap || !isAvailable) return;

    try {
      const accessibility = getAccessibilityConfig();
      
      if (accessibility.reduceMotion) {
        // Subtle changes for reduced motion
        if (isEntering) {
          gsap.to(card, {
            scale: 1.01,
            duration: ANIMATION_DURATIONS.FAST,
            ease: ANIMATION_EASINGS.POWER2_OUT
          });
        } else {
          gsap.to(card, {
            scale: 1,
            duration: ANIMATION_DURATIONS.FAST,
            ease: ANIMATION_EASINGS.POWER2_OUT
          });
        }
        return;
      }

      if (isEntering) {
        gsap.to(card, {
          y: -10,
          scale: 1.02,
          boxShadow: `0 20px 40px rgba(255, 51, 102, 0.3), 0 0 30px rgba(74, 144, 226, 0.2)`,
          duration: ANIMATION_DURATIONS.NORMAL,
          ease: ANIMATION_EASINGS.POWER2_OUT
        });
      } else {
        gsap.to(card, {
          y: 0,
          scale: 1,
          boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)",
          duration: ANIMATION_DURATIONS.NORMAL,
          ease: ANIMATION_EASINGS.POWER2_OUT
        });
      }
    } catch (error) {
      handleError(error as Error);
    }
  };

  return (
    <section 
      ref={sectionRef}
      className="min-h-screen bg-[#1A1A1A] py-20 px-4 relative"
      id="portfolio"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-radial opacity-50" />
      
      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Title */}
        <div className="text-center mb-16">
          <h2 
            ref={titleRef}
            className="text-4xl md:text-5xl lg:text-6xl font-black text-[#F5F5F5] mb-6 tracking-wide"
          >
            FEATURED WORK
          </h2>
          <p className="text-lg md:text-xl font-light tracking-widest text-gray-400 uppercase">
            Creative Solutions & Digital Experiences
          </p>
        </div>

        {/* Project Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
          {PROJECTS.map((project, index) => (
            <div
              key={project.id}
              ref={(el) => {
                if (el) cardsRef.current[index] = el;
              }}
              className="group relative bg-[#0F0F0F] rounded-lg overflow-hidden border border-gray-800"
              style={{
                boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)",
                willChange: "transform"
              }}
              onMouseEnter={() => handleCardHover(index, true)}
              onMouseLeave={() => handleCardHover(index, false)}
            >
              {/* Coming Soon Badge */}
              <div className="absolute top-4 right-4 z-20">
                <span className="bg-gradient-to-r from-[#FF3366] to-[#4A90E2] text-white text-xs font-bold px-3 py-1 rounded-full uppercase tracking-wide">
                  Coming Soon
                </span>
              </div>

              {/* Project Image/Placeholder */}
              <div className="relative h-64 overflow-hidden">
                <img 
                  src={project.placeholder}
                  alt={project.title}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-[#0F0F0F] via-transparent to-transparent opacity-60" />
              </div>

              {/* Project Content */}
              <div className="p-6">
                <div className="mb-3">
                  <span className="text-[#4A90E2] text-sm font-medium uppercase tracking-wider">
                    {project.type}
                  </span>
                </div>
                
                <h3 className="text-xl md:text-2xl font-bold text-[#F5F5F5] mb-3 group-hover:text-[#FF3366] transition-colors duration-300">
                  {project.title}
                </h3>
                
                <p className="text-gray-400 leading-relaxed mb-4">
                  {project.description}
                </p>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <button className="px-4 py-2 bg-[#FF3366] text-white rounded-md text-sm font-medium hover:bg-[#E02A56] transition-colors duration-300 opacity-50 cursor-not-allowed">
                    View Project
                  </button>
                  <button className="px-4 py-2 border border-[#4A90E2] text-[#4A90E2] rounded-md text-sm font-medium hover:bg-[#4A90E2] hover:text-white transition-all duration-300 opacity-50 cursor-not-allowed">
                    Live Demo
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-gray-400 mb-6">
            More projects coming soon. Stay tuned for updates.
          </p>
          <div className="inline-flex items-center space-x-2 text-[#FF3366]">
            <span className="w-2 h-2 bg-[#FF3366] rounded-full animate-pulse"></span>
            <span className="text-sm font-medium uppercase tracking-wider">
              Currently in development
            </span>
          </div>
        </div>
      </div>
    </section>
  );
}